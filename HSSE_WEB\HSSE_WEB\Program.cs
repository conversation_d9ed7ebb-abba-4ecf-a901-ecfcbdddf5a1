using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using HSSE_Domain.Models;
using HSSE_Service.DataManager;
using HSSE_Service.Handler;
using HSSE_Service.Helper;
using HSSE_Service.InterfaceService;
using HSSE_WEB.AutoMapper;
using HSSE_WEB.Middleware;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);
builder.Services.AddDbContext<HsseDbLatestContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("dbconn")), ServiceLifetime.Scoped);
string firebaseConfigPath = Path.Combine(AppContext.BaseDirectory, "FirebaseConfig", "hsse-admin-2503.json");

// Initialize Firebase with AppOptions
var firebaseApp = FirebaseApp.Create(new AppOptions
{
    Credential = GoogleCredential.FromFile(firebaseConfigPath).CreateScoped(new string[]
    {
        "https://www.googleapis.com/auth/firebase.storage",   // Firebase Storage Scope
        "https://www.googleapis.com/auth/cloud-platform"     // Google Cloud Platform Scope (for broader access)
    })
});
// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddAutoMapper(typeof(AutoMapperProfile));
builder.Services.Configure<IISServerOptions>(options =>
{
    options.MaxRequestBodySize = 104857600; // 100 MB
});
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.Limits.MaxRequestBodySize = 104857600; // 100 MB
});
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
}).AddJwtBearer(o =>
{
    o.TokenValidationParameters = new TokenValidationParameters
    {
        ValidIssuer = builder.Configuration["Jwt:Issuer"],
        ValidAudience = builder.Configuration["Jwt:Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(builder.Configuration["Jwt:Key"])),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ClockSkew = TimeSpan.Zero
    };
});

// --------------------------register services---------------------------
builder.Services.AddTransient<IUserService, UserService>();
builder.Services.AddTransient<IUserRoleService, UserRoleService>();
builder.Services.AddTransient<IFacilityService, FacilityService>();
builder.Services.AddTransient<IAnnouncementService, AnnouncementService>();
builder.Services.AddTransient<IGroupService, GroupService>();
builder.Services.AddTransient<IEventService, EventService>();
builder.Services.AddTransient<INewsletterService, NewsletterService>();
builder.Services.AddTransient<IPostService, PostService>();
builder.Services.AddTransient<IInspectionService, InspectionService>();
builder.Services.AddTransient<IDocumentLibraryService, DocumentLibraryService>();
builder.Services.AddTransient<IPermissionService, PermissionService>();
builder.Services.AddTransient<IFeedbackService, FeedbackService>();
builder.Services.AddScoped<FirebaseStorageHelper>();

builder.Services.AddHttpContextAccessor(); 

builder.Services.AddTransient<BearerTokenHandler>();

builder.Services.AddHttpClient("AuthenticatedClient").AddHttpMessageHandler<BearerTokenHandler>();

builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options => {
    //options.IdleTimeout = TimeSpan.FromMinutes(600); // Set your desired session timeout.
    options.IdleTimeout = TimeSpan.FromDays(36500); // Effectively no timeout
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});
builder.Services.AddHttpClient();
var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseStatusCodePagesWithReExecute("/Home/Error");

    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(@"C:\HSSE-Announcements"),
    RequestPath = "/ExternalFiles"
});
app.UseRouting();
app.UseSession();

app.UseAuthorization();

app.MapStaticAssets();


app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Login}/{action=Index}/{id?}")
    .WithStaticAssets();

app.UseMiddleware<GlobalExceptionMiddleware>();
app.Run();
