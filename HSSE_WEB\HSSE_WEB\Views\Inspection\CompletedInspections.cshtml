﻿@*
    For more information on enabling MVC for empty projects, visit https://go.microsoft.com/fwlink/?LinkID=397860
*@
@{
}
<div class="col-12 grid-margin">
    <div class="card shadow-sm">
        <div class="card-body">
            <h4 class="card-title">Complete Inspections</h4>
            <div id="assigned-inspections-list">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover" id="inspectionsTable">
                        <thead class="thead-light">
                            <tr>
                                <th>Ref No</th>
                                <th>Location</th>
                                <th>Date</th>
                                <th>Inspector</th>
                                <th>Status</th>
                                <th>Verification</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="inspectionsTableBody">
                            <!-- Rows inserted via JS -->
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Inspections will be loaded here by JS -->
        </div>
    </div>
</div>
<!-- Modal to show card preview -->
<div class="modal fade" id="inspectionPreviewModal" tabindex="-1" role="dialog" aria-labelledby="inspectionPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Inspection Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="inspectionPreviewContent">
                <!-- Card content will be loaded here -->
            </div>
        </div>
    </div>
</div>
<script>
     var completeInspection =  '@Url.Action("CompleteInspection", "Inspection")';
     var getInspectionsByActionParty =   '@Url.Action("GetInspectionsByActionParty", "Inspection")?status=';
    var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';
    var status = 1;
        const basePath = '@Url.Content("~/")'; // returns '/UEMS/HSSEWeb/' in production

</script>
@section Scripts {
    <script src="~/js/Inspection/assignedInspections.js"></script>
} 