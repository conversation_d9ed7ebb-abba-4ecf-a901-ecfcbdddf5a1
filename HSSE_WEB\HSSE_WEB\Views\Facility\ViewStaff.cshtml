﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel

<div class="card mt-4" data-permission="view">
    <div class="card-body">
        <h4 class="card-title">Staff</h4>
        <div class="row mb-3">
            <div class="col-md-4">
                <label>Filter by Role:</label>
                <select id="roleFilter" class="form-control">
                    <option value="">-- Select Role --</option>
                    @foreach (var org in Model.Roles)
                    {
                        <option value="@org.RoleId">@org.RoleName</option>
                    }
                </select>
            </div>
            <div class="col-md-4">
                <label>Filter by Facility:</label>
                <select id="facilityFilter" class="form-control">
                    <option value="">-- Select Facility --</option>
                    @foreach (var org in Model.Facilities)
                    {
                        <option value="@org.FacilityId">@org.FacilityName</option>
                    }
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button class="btn btn-primary" onclick="applyFilters()">Apply Filters</button>
            </div>

        </div>

        <div class="table-responsive">
            <table id="order-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Username</th>
                        <th>Role Name</th>
                        <th>Facility Name</th>
                        @* <th>Actions</th> *@
                    </tr>
                </thead>
                <tbody id="userRoleTableBody">
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
    var getUsers = '@Url.Action("GetAllUsers", "Facility")';
</script>
@section Scripts {
    <script src="~/js/facility/viewStaff.js"></script>
}