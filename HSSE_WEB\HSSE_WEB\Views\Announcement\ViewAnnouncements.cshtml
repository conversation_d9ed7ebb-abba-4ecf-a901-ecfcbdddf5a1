﻿<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Announcements</h4>
        <div class="table-responsive">
            <table id="order-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Title</th>
                        <th>Schedule At</th>
                        <th>Expiry At</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="announcementTableBody">
                </tbody>
            </table>
        </div>
    </div>
</div>
<div class="modal fade" id="viewAnnouncementModal" tabindex="-1" aria-labelledby="viewAnnouncementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Announcement Details</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <h3 class="fw-bold text-primary mb-2" id="announcementTitle">Title</h3>

                    <div class="mb-2">
                        <span class="fw-semibold text-dark">Description:</span>
                        <div id="announcementDescription" class="text-muted border rounded p-2 bg-light mt-1" style="white-space: pre-wrap;"></div>
                    </div>
                </div>


                <div class="row mb-3">
                    <div class="col-md-6">
                        <strong>Schedule At:</strong>
                        <span id="announcementSchedule" class="text-muted ms-2">-</span>
                    </div>
                    <div class="col-md-6">
                        <strong>Expiry At:</strong>
                        <span id="announcementExpiry" class="text-muted ms-2">-</span>
                    </div>
                </div>

                <hr>

                <div>
                    <h6 class="fw-semibold">Receivers</h6>
                    <div id="announcementReceiversDisplay" class="d-flex flex-wrap gap-2 mt-2">
                        <!-- Dynamically injected badges will appear here -->
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<script>

    var getAnnouncements = '@Url.Action("GetAllAnnouncements", "Announcement")';
          var  deleteAnnouncements = '@Url.Action("DeleteAnnouncement", "Announcement")?id=';
      var  getAnnouncementsById =   '@Url.Action("Details", "Announcement")?id=';
                          const appBaseUrl = '@Url.Content("~/")'; // ensures base path works on live

</script>
@section Scripts {
    <script src="~/js/Announcement/viewAnnouncements.js"></script>
} 