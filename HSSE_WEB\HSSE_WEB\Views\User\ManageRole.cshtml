﻿@model List<HSSE_Models_Dto.ModelsDto.MstUsersRoleDto>

<div class="main-panel">
    <div class="content-wrapper">

        <!-- Form to Add/Edit Role -->
        <div class="card" data-permission="create">
            <div class="card-body">
                <h4 class="card-title">Create / Edit Role</h4>
                <form id="roleForm">
                    <input type="hidden" name="RoleId" id="RoleId" value="0" />
                    <div class="form-group row">
                        <label class="col-sm-2 col-form-label">Role Name<span class="text-danger">*</span></label>
                        <div class="col-sm-6">
                            <input type="text" class="form-control" name="RoleName" placeholder="Enter role name" id="userRoleId" />
                        </div>
                        <label class="col-sm-1 col-form-label">Active</label>
                        <div class="col-sm-2 ">
                            <div class="form-check">
                                <label class="form-check-label">
                                <input type="checkbox" class="form-check-input" name="SeededRole" id="SeededRole" checked/>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-sm-12 text-left">
                            <button type="submit" class="btn btn-outline-primary btn-icon-text"><i class="mdi mdi-file-check btn-icon-prepend"></i>Submit</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Table to Show Existing Roles -->
        <div class="card mt-4" data-permission="view">
            <div class="card-body">
                <h4 class="card-title">Existing Roles</h4>
                <div class="table-responsive">
                    <table id="order-listing" class="table">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Role Name</th>
                                <th>Seeded</th>
                                <th>Status</th> <!-- New column for active/deactivated status -->
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="rolesTableBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editRoleModal" tabindex="-1" aria-labelledby="editRoleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">

            <div class="modal-header">
                <h5 class="modal-title" id="editUserLabel">Edit Role</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>

            <div class="modal-body">
                <input type="hidden" id="editRoleId" />
                <div class="mb-3">
                    <label for="editRoleName" class="form-label">Role Name</label>
                    <input type="text" class="form-control" id="editRoleName" />
                </div>
         @*        <div class="form-group row">
                    <label class="col-sm-3 col-form-label">Seeded Role</label>

                <div class="form-check">
                    <label class="form-check-label">
                            <input type="checkbox" class="form-check-input" id="editIsSeeded" />
                    </label>
                </div>
                </div>
 *@
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateRole()">Update Role</button>
            </div>

        </div>
    </div>
</div>
<script>
    var getAllRoles = '@Url.Action("GetAllRoles", "User")';
    var toggleRoleActivation = '@Url.Action("ToggleRoleActivation", "User")';
    var getAllUserRoleById = '@Url.Action("GetAllUserRoleById", "User")';
    var createRole = '@Url.Action("CreateRole", "User")';
</script>
@section Scripts {
    <script src="~/js/User/manageRole.js"></script>
}
