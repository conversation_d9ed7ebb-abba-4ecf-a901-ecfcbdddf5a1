﻿@using HSSE_Models_Dto.ViewModels
@model AnnouncementViewModel

@{
    var imgBasePath = Url.Content("~/"); // returns '/UEMS/HSSEWeb/' in production
}
<div class="container">
    @if (Model == null)
    {
        <div class="alert alert-danger text-center" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            Unable to load announcement details. Please try again later.
        </div>
        <div class="text-center mt-3">
            <a href="/Announcements/DisplayUserAnnouncements" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left me-1"></i> Back to Announcements
            </a>
        </div>
    }
    else
    {
        var announcement = Model;

        <div class="card shadow-lg border-0">
            <!-- Header with Title -->
            <div class="card-header  py-3 d-flex justify-content-between align-items-center flex-wrap">
                <!-- Title on the left -->
                <h3 class="card-title mb-0">
                    <i class="bi bi-megaphone-fill me-2"></i> @announcement.Title
                </h3>

                
            </div>


            <div class="card-body">

                <!-- Image -->
                @if (!string.IsNullOrEmpty(announcement.AnnouncementDocument))
                {
                    <div class="mb-4 text-left">
                        <img src="@announcement.AnnouncementDocument"
                             class="img-fluid rounded shadow-sm"
                             style="max-height: 150px; object-fit: cover;"
                             alt="Announcement Image" />
                    </div>
                }

                <!-- Description -->
                <div class="mb-4 fs-5 text-muted">
                    @Html.Raw(announcement.Description)
                </div>
                <!-- Download button on the right -->
                <div class="mb-3 d-flex flex-wrap gap-3 align-items-center">
                @if (!string.IsNullOrEmpty(announcement.Attachment))
                {
                    <a href="@announcement.Attachment"
                       class="btn btn-outline-info btn-sm"
                       target="_blank"
                       download>
                        <i class="mdi mdi-folder-download"></i>@announcement.Attachment
                    </a>
                }
                </div>
                <!-- Meta Info -->
                <div class="mb-3 d-flex flex-wrap gap-3 align-items-center">
                    <span class="badge bg-light text-dark border mr-2">Published by: <strong>@announcement.PublishedBy</strong></span>

                    @if (announcement.ScheduleAt.HasValue)
                    {
                        <span class="badge bg-light text-dark border">Published on: <strong>@announcement.ScheduleAt.Value.ToString("MMM dd, yyyy")</strong></span>
                    }
                </div>

         @*        <!-- Action Buttons -->
                <div class="d-flex flex-wrap gap-3 mt-4">
                    <a asp-controller="Announcement"
                       asp-action="DisplayUserAnnouncements"
                       class="btn btn-outline-primary">
                        <i class="bi bi-arrow-left-circle me-1"></i> Back
                    </a>
                </div> *@
            </div>
        </div>
    }
</div>


