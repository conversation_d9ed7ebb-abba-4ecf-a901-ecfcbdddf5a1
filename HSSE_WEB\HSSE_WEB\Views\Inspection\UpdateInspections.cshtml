﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel

@{
    ViewData["Title"] = "My Inspections";
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">My Inspections</h4>
            <div class="table-responsive">
                <table id="userInspectionsTable" class="table table-hover">
                    <thead>
                        <tr>
                            <th>Reference No</th>
                            <th>Facility</th>
                            <th>Date</th>
                            <th>Inspector Name</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded by DataTable -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
            var getUserInspections = '@Url.Action("GetUserInspections", "Inspection")';
            const basePath = '@Url.Content("~/")';
</script>
@section Scripts {

    <script src="~/js/Inspection/updateInspection.js"></script>
} 