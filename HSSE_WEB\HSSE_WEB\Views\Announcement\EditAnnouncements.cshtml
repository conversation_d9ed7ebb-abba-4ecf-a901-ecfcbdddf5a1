﻿@model HSSE_Models_Dto.ViewModels.AddUserViewModel


<div class="card mt-4">
    <div class="card-body">
        <h4 class="card-title">Announcements</h4>
        <div class="table-responsive">
            <table id="order-listing" class="table">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>Title</th>
                        <th>Schedule At</th>
                        <th>Expiry At</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="announcementEditTableBody">
                </tbody>
            </table>
        </div>
    </div>
</div>


<!-- Edit Announcement Modal -->
<div class="modal fade" id="announcementModal" tabindex="-1" aria-labelledby="announcementModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Announcement</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <form id="announcementForm" class="form-sample" method="post">
                    <input type="hidden" id="AnnouncementId" name="AnnouncementId" />
                    <!-- Title & Status -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Title<span class="text-danger">*</span></label>
                                <div class="col-sm-9">
                                    <input type="text" class="form-control" name="Title" id="Title" placeholder="Enter Title" required />
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Status<span class="text-danger">*</span></label>
                                <div class="col-sm-9">
                                    <select name="Status" id="Status" class="form-control">
                                        <option value="0">Draft</option>
                                        <option value="1">Published</option>
                                        <option value="2">Archived</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Schedule & Expiry -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Schedule At</label>
                                <div class="col-sm-9">
                                    <input type="datetime-local" class="form-control" name="ScheduledAt" id="ScheduledAt" />
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Expiry At</label>
                                <div class="col-sm-9">
                                    <input type="datetime-local" class="form-control" name="ExpiryAt" id="ExpiryAt" />
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                      @*   <div class="col-md-6">
                            <div class="form-group row">
                                <label for="FacilityId" class="col-sm-3 col-form-label">Facility</label>
                                <div class="col-sm-9">
                                    <select name="FacilityId" id="edit-announcement-FacilityId" class="form-control">
                                        <option value="">-- Select Facility --</option>

                                        @foreach (var user in Model.Facilities)
                                        {
                                            <option value="@user.FacilityId">@user.FacilityName</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        </div> *@
                     
                    </div>
                    <div class="mb-3 row">
                        <label class="col-sm-2 col-form-label">Send To<span class="text-danger">*</span></label>
 <div class="col-sm-4 d-flex align-items-center gap-3" style="height: fit-content;">
     <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input" type="radio" name="SendTo" id="sendToUser" value="User" checked>
                                    @* <label class="form-check-label" for="sendToUser">User</label> *@
                                    User
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">

                                    <input class="form-check-input" type="radio" name="SendTo" id="sendToGroup" value="Group">
                                    @* <label class="form-check-label" for="sendToGroup">Group</label> *@
                                    Group
                                </label>
                            </div>
                        </div>

                        <!-- Group Dropdown -->
                        <div class="col-md-6" id="groupSection" style="display: none;">

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Group</label>
                                <div class="col-sm-9">
                                    <select name="GroupId" id="GroupId" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                        <option value="">-- Select Group --</option>
                                        @foreach (var group in Model.Group)
                                        {
                                            <option value="@group.GroupId">@group.GroupName</option>
                                        }
                                    </select>
                                </div>
                            </div>

                        </div>

                        <!-- User Dropdown -->
                        <div class="col-md-6" id="userSection">

                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Users</label>
                                <div class="col-sm-9">
                                    <select name="UserIds" id="UserIds" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                        @foreach (var user in Model.ExistingUsers)
                                        {
                                            <option value="@user.UserId">@user.Username</option>
                                        }
                                    </select>
                                </div>
                            </div>


                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">

                                <label class="col-sm-3 col-form-label">Select Category<span class="text-danger">*</span></label>
                                <div class="col-sm-9">
                                    @*    <select name="UserId" class="form-control" required>
                                <option value="">-- Select User --</option>
                                @foreach (var role in Model.ExistingUsers)
                                {
                                    <option value="@role.UserId">@role.Username</option>
                                }
                            </select> *@
                                    <div id="user-typeahead">
                                        <input type="text" class="typeahead form-control" id="edit-user-search" placeholder="Search Category">
                                        <input type="hidden" id="editSelectedCategoryId" name="editSelectedCategoryId" />
                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Attachment</label>
                                <div class="col-sm-9">
                                    <input type="hidden" id="ExistingAttachmentFile" />
                                    <input type="file" name="img[]" class="file-upload-default d-none" id="Attachment">
                                    <div class="input-group">
                                        <input type="text" class="form-control file-upload-info" id="announcementAttachmentFileName" readonly placeholder="Choose attachment" />
                                        <div class="input-group-append">
                                            <button class="btn btn-primary btn-sm" type="button" id="announcementAttachmentBtn">Upload</button>
                                            <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAnnouncementAttachmentBtn">Remove</button>
                                        </div>
                                   @*      <input type="text" class="form-control file-upload-info attachment" disabled placeholder="Upload Attachment">
                                        <div class="input-group-append">
                                            <button class="file-upload-browse btn btn-primary btn-sm" type="button">Upload</button>
                                        </div> *@
                                    </div>
                                </div>
                            </div>
                        </div>
                    
                    </div>
                    <div class="row">

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-sm-3 col-form-label">Thumbnail</label>
                                <div class="col-sm-9">
                                    <div class="custom-file-upload w-100">
                                        <input type="hidden" id="ExistingThumbnailPath" />

                                        <input type="file" class="d-none" id="AnnouncementFile" name="ThumbnailImage" accept="image/*" />
                                        <div class="input-group">
                                            <input type="text" class="form-control file-upload-info" id="announcementThumbnailFileName" readonly placeholder="Choose Thumbnail" />
                                            <div class="input-group-append">
                                                <button class="btn btn-primary btn-sm" type="button" id="announcementThumbnailBtn">Upload</button>
                                                <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeAnnouncementThumbnailBtn">Remove</button>
                                            </div>
                                        </div>
                                        <div class="mt-2 d-none" id="announcementThumbnailPreviewContainer">
                                            <img id="announcementThumbnailPreview" src="" class="img-thumbnail" style="max-width: 250px;" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <!-- Description -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group row">
                                <label class="col-sm-2 col-form-label">Description<span class="text-danger">*</span></label>
                                <div class="col-sm-12">
                                    <textarea name="Description" id="tinyMceExample" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                    </div>

                    <!-- Buttons -->
                    <div class="row">
                        <div class="col-md-12 text-end">
                            <button type="submit" class="btn btn-primary">Update</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
         var existingUsers = [
    @foreach (var user in Model.AnnouncementsCategory)
    {
        @: { id: "@user.AnnoucementCategoryId", name: "@user.AnnoucementCategoryName" },
    }
    ];
    var getAnnouncementsByUserID = '@Url.Action("GetAllAnnouncementsByUserId", "Announcement")';
    var  getAnnouncementsById = '@Url.Action("GetAnnouncementDetailsById", "Announcement")?id=';
    var updateAnnouncement = '@Url.Action("UpdateAnnouncement", "Announcement")';
        var toggleAnnouncementStatus = '@Url.Action("ToggleAnnouncementStatus", "Announcement")';
                  var  deleteAnnouncements = '@Url.Action("DeleteAnnouncement", "Announcement")?id=';
                          const appBaseUrl = '@Url.Content("~/")'; // ensures base path works on live
</script>
@section Scripts {
    <script src="~/js/Announcement/editAnnounsments.js"></script>
}