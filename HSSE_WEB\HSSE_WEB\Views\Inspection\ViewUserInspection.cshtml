﻿@using Microsoft.AspNetCore.Html
@model HSSE_Models_Dto.ViewModels.AddUserViewModel
@functions {
    public IHtmlContent RenderStatusBadge(int status)
    {
        switch (status)
        {
            case 1:
                return new HtmlString("<span class=\"badge badge-success\">Complete</span>");
            case 2:
                return new HtmlString("<span class=\"badge badge-warning\">Pending</span>");
            case 3:
                return new HtmlString("<span class=\"badge badge-danger\">Cannot Rectify</span>");
            case 4:
                return new HtmlString("<span class=\"badge badge-info\">Rectified</span>");
            default:
                return new HtmlString("<span class=\"badge badge-secondary\">Unknown</span>");
        }
    }
   public IHtmlContent RenderVerificationBadge(int varification)
    {
        switch (varification)
        {
            case 0:
                return new HtmlString("<span class=\"badge badge-warning\">Not Done</span>");
            case 1:
                return new HtmlString("<span class=\"badge badge-success\">Accept</span>");
            case 2:
                return new HtmlString("<span class=\"badge badge-danger\">Reject</span>");
            case 3:
                return new HtmlString("<span class=\"badge badge-info\">KIV</span>");
            default:
                return new HtmlString("<span class=\"badge badge-secondary\">Pending Verification</span>");
        }
    }
}
@{
    var isAppAdmin = Convert.ToBoolean(Context.Session.GetString("IsAppAdmin"));
    var isDeptAdmin = Convert.ToBoolean(Context.Session.GetString("IsDepartmentAdmin"));
}
<div class="col-12 grid-margin">
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Edit Inspection</h4>

            <form id="inspectionForm" data-action="@Url.Action("InsertOrUpdateInspection", "Inspection")" enctype="multipart/form-data">
                <div class="row">
                    <div class="col-md-6">
                        <input type="hidden" id="inspectionId" name="inspectionId" value="@Model.inspectionDetails.InspectionId" readonly />

                        <div class="form-group ">
                            <label class="col-sm-4 col-form-label">Reference No.</label>
                         
                                <input type="text" class="form-control font-weight-bold" id="ReferenceNo" name="ReferenceNo" value="@Model.inspectionDetails.ReferenceNo" readonly />
                         
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group ">
                            <label class="col-sm-4 col-form-label">Date and Time</label>

                            <input type="text" class="form-control" id="DateTime" name="DateTime" value="@Model.inspectionDetails.InspectionDate.ToString("dd-MM-yyyy")" readonly />
                       
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Facility <span class="text-danger">*</span></label>
                      
                            <select class="form-control" id="FacilityId" name="FacilityId" disabled>
    <option value="">-- Select Facility --</option>
    @foreach (var facility in (List<HSSE_Models_Dto.ModelsDto.MstFacilityDto>)ViewBag.FacilityList)
    {
                                    var isSelected = (Model != null && Model.inspectionDetails.FacilityId == facility.FacilityId);
                                    <option value="@facility.FacilityId" selected=@isSelected>@facility.FacilityName</option>
    }
</select>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Location <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="LocationId" name="LocationId" value="@Model.inspectionDetails.Location" placeholder="Location" readonly />
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Type of Inspection <span class="text-danger">*</span></label>
                            <div id="user-typeahead">
                                <input type="text" class="form-control typeahead" id="user-search" value="@Model.inspectionDetails.TypeOfInspectionName" placeholder="Search Category" readonly />
                                <input type="hidden" id="selectedCategoryId" name="selectedCategoryId" value="@Model.inspectionDetails.TypeOfInspection" />
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Person Conducting Inspection</label>
                            <input type="text" class="form-control" id="InspectorName" name="InspectorName" value="@Model.inspectionDetails.InspectorName" readonly />
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label>Contact Person <span class="text-danger">*</span></label>
                            <select class="form-control" id="ContactPersonId" data-selected="@Model.inspectionDetails?.MstInspectionItems.FirstOrDefault().ContactPersonId" name="ContactPersonId" disabled>
                                <option value="">Select Contact Person</option>
                            </select>
                        </div>
                    </div>
                </div>
                <!-- Add Observation Button -->
                <div class="row mt-4">
                    <div class="col-md-9">
                        <button type="button" class="btn btn-outline-primary" id="addObservationBtn">
                            + Add Observation
                        </button>
                    </div>
             @*        <div class="form-group">
                        <div class="form-check">
                            <label class="form-check-label">
                                <input type="checkbox" class="form-check-input" id="filterAssigned"/>
                                Show assigned observations
                            </label>
                        </div>
                    </div> *@
                </div>

                <!-- Observation Table -->
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="table-responsive" >
                            @* style="max-height: 400px; overflow-y: auto; overflow-x: auto;" *@
                            <table class="table table-bordered" id="observationTable" >
                                @* style="min-width: 1200px;" *@
                                <thead style="position: sticky; top: 0; z-index: 2;background: #dedede;">
                                    <tr>
                                        <th>#</th>
                                        <th>Observation</th>
                                        <th>Recommendation</th>
                                        <th>Pictures</th>
                                        <th>Action Party</th>
                                        <th>Observation Type</th>
                                    
                                        <th>Verification Status</th>
                                        <th>Status</th>
                                        <th>Action</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @if (Model.inspectionDetails.MstInspectionItems != null && Model.inspectionDetails.MstInspectionItems.Any())
                                    {
                                        int index = 1;
                                        foreach (var item in Model.inspectionDetails.MstInspectionItems)
                                        {
                                            <tr data-index="@index">
                                                <td>@index</td>
                                                @{
                                                    var obsHtml = item.Observation ?? "";
                                                    var recHtml = item.Recommendation ?? "";

                                                    var obsShort = obsHtml.Length > 200 ? obsHtml.Substring(0, 200) : obsHtml;
                                                    var recShort = recHtml.Length > 200 ? recHtml.Substring(0, 200) : recHtml;

                                                    var showObsToggle = obsHtml.Length > 200;
                                                    var showRecToggle = recHtml.Length > 200;
                                                }


                                                <td style="min-width: 400px;white-space: normal;">
                                                    <div id="obs-short-@index">@Html.Raw(obsShort)@(showObsToggle ? "..." : "")</div>
                                                    <div id="obs-full-@index" style="display:none;">@Html.Raw(obsHtml)</div>
                                                    @if (showObsToggle)
                                                    {
                                                        <a href="javascript:void(0);" class="text-primary toggle-link" data-type="obs" data-index="@index">See more</a>
                                                    }
                                                </td>

                                                <td style="min-width: 400px;white-space: normal;">
                                                    <div id="rec-short-@index">@Html.Raw(recShort)@(showRecToggle ? "..." : "")</div>
                                                    <div id="rec-full-@index" style="display:none;">@Html.Raw(recHtml)</div>
                                                    @if (showRecToggle)
                                                    {
                                                        <a href="javascript:void(0);" class="text-primary toggle-link" data-type="rec" data-index="@index">See more</a>
                                                    }
                                                </td>



                                                <td style="min-width: 300px;">
                                                    <div id="<EMAIL>" class="carousel slide" data-ride="carousel">
                                                        <div class="carousel-inner">

                                                            @if (!string.IsNullOrEmpty(item.ObservationMediaUrl))
                                                            {
                                                                <div class="carousel-item active">
                                                                    <div class="position-relative">
                                                                        <img src="@Url.Content(item.ObservationMediaUrl)" class="d-block w-100" style="height: 100px; object-fit: contain; border-radius: 0px !important;" alt="Observation Image" />
                                                                        <span class="badge badge-info position-absolute" style="top: 10px; left: 10px;">Observation</span>
                                                                    </div>
                                                                </div>
                                                            }

                                                            @if (item.RecommendationMediaUrl != null)
                                                            {
                                       
                                                                    <div class="carousel-item">
                                                                        <div class="position-relative">
                                                                        <img src="@Url.Content(item.RecommendationMediaUrl)" class="d-block w-100" style="height: 100px; object-fit: contain; border-radius: 0px !important;" alt="Recommendation Image" />
                                                                            <span class="badge badge-warning position-absolute" style="top: 10px; left: 10px;">Recommendation</span>
                                                                        </div>
                                                                    </div>
                                           
                                                            }

                                                            @if (item.AfterImagePath != null)
                                                            {
                                                          
                                                                    <div class="carousel-item">
                                                                        <div class="position-relative">
                                                                        <img src="@Url.Content(item.AfterImagePath)" class="d-block w-100" style="height: 100px; object-fit: contain;" alt="After Image" />
                                                                            <span class="badge badge-success position-absolute" style="top: 10px; left: 10px;">After</span>
                                                                        </div>
                                                                    </div>
                                                                
                                                            }


                                                        </div>
                                                        <a class="carousel-control-prev" href="#<EMAIL>" role="button" data-slide="prev">
                                                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                                            <span class="sr-only">Previous</span>
                                                        </a>
                                                        <a class="carousel-control-next" href="#<EMAIL>" role="button" data-slide="next">
                                                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                                            <span class="sr-only">Next</span>
                                                        </a>
                                                    </div>
                                                </td>


                                                <td data-ids="@item.ActionPartyId">
                                                    @{
                                                        var partyNames = item.ActionPartyNames?.Split(',', StringSplitOptions.RemoveEmptyEntries) ?? new string[0];
                                                        var badgeClasses = new[] { "badge-danger", "badge-info", "badge-success", "badge-warning", "badge-secondary" };
                                                        int badgeIndex = 0;
                                                    }

                                                    <div class="d-flex flex-wrap" style="min-width: 160px;">
                                                        @foreach (var name in partyNames)
                                                        {
                                                            var badgeClass = badgeClasses[badgeIndex % badgeClasses.Length];
                                                            <span class="badge @badgeClass mr-1 mb-1">@name.Trim()</span>
                                                            badgeIndex++;
                                                        }
                                                    </div>
                                                </td>
                             

                                                <td>
                                                    @if (item.ObservationType == 1)
                                                    {
                                                        <span class="badge badge-success">Positive</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge badge-warning">For Action</span>
                                                    }
                                                </td>
                                                <td>@RenderVerificationBadge(item.Verification ?? 0)</td>
                                                <td>@RenderStatusBadge(item.Status ?? 0)</td>
                                    
                                                <td>
                                                    <div class="d-flex gap-2">
                                                        <!-- Only for Action Party: Show Status Update -->
                              @*                           @if (!isAppAdmin && !isDeptAdmin)
                                                        {
                                                            <div class="dropdown mr-2">
                                                                <button class="btn btn-sm btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                                    Update Status
                                                                </button>
                                                                <div class="dropdown-menu">
                                                                    @if (item.Status != 1)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="openCompleteInspectionModal(@item.ItemId)">
                                                                            <i class="mdi mdi-check text-success"></i> Complete
                                                                        </button>
                                                                    }
                                                                    @if (item.Status != 2)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="updateStatus(@item.ItemId, 2, @item.ObservationType)">
                                                                            <i class="mdi mdi-clock text-warning"></i> Pending
                                                                        </button>
                                                                    }
                                                                    @if (item.Status != 3)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="updateStatus(@item.ItemId, 3, @item.ObservationType)">
                                                                            <i class="mdi mdi-close text-danger"></i> Cannot Rectify
                                                                        </button>
                                                                    }
                                                                </div>
                                                            </div>

            
                                                        }
 *@
                                                        <!-- Only for Admins: Show Verification if Status is Complete -->
                                                   @*      @if (isAppAdmin || isDeptAdmin)
                                                        { *@

                                                      @*       @if (item.Status == 1)
                                                            {
                                                            var idx = $"verify_{item.ItemId}";
                                                            <div class="dropdown mr-2">
                                                                <button class="btn btn-sm btn-primary" type="button" id="dropdownVerification@idx" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Update Verification">
                                                                   Update Verification
                                                                </button>
                                                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownVerification@idx">
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 0)">Not Done</a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 1)">Accept</a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 2)">Reject</a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 3)">KIV</a>
                                                                </div>
                                                            </div>
                                                            } *@
                                                        @*     <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteObservation(@item.ItemId, this)">
                                                                <i class="mdi mdi-delete"></i>
                                                            </button> *@
                                                        @* } *@
                                                        @if (isAppAdmin || isDeptAdmin)
                                                        {
                                                            <div class="dropdown mr-2">
                                                                <button class="btn btn-sm btn-icon btn-outline-primary" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Update Status">
                                                                    <i class="fa fa-gear"></i>
                                                                </button>
                                                                <div class="dropdown-menu">
                                                                    @if (item.Status != 1)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="openCompleteInspectionModal(@item.ItemId)">
                                                                            <i class="mdi mdi-check text-success mr-1"></i> Complete
                                                                        </button>
                                                                    }
                                                                    @if (item.Status != 2)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="updateStatus(@item.ItemId, 2, @item.ObservationType)">
                                                                            <i class="mdi mdi-clock text-warning mr-1"></i> Pending
                                                                        </button>
                                                                    }
                                                                    @if (item.Status != 3)
                                                                    {
                                                                        <button type="button" class="dropdown-item" onclick="updateStatus(@item.ItemId, 3, @item.ObservationType)">
                                                                            <i class="mdi mdi-close text-danger mr-1"></i> Cannot Rectify
                                                                        </button>
                                                                    }
                                                                </div>
                                                            </div>
                                                        }

                                                        @if (isAppAdmin || isDeptAdmin || item.Status == 1)
                                                        {
                                                            var idx = $"verify_{item.ItemId}";
                                                            <div class="dropdown mr-2">
                                                                <button class="btn btn-sm btn-icon btn-outline-secondary" type="button" id="dropdownVerification@idx" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" title="Update Verification">
                                                                    <i class="mdi mdi-clipboard-text-outline"></i>
                                                                </button>
                                                                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownVerification@idx">
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 0)">
                                                                        <i class="mdi mdi-alert-circle-outline text-muted mr-1"></i> Not Done
                                                                    </a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 1)">
                                                                        <i class="mdi mdi-check-bold text-success mr-1"></i> Accept
                                                                    </a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 2)">
                                                                        <i class="mdi mdi-close text-danger mr-1"></i> Reject
                                                                    </a>
                                                                    <a class="dropdown-item" href="#" onclick="updateVerification(@item.ItemId, 3)">
                                                                        <i class="mdi mdi-help-circle-outline text-info mr-1"></i> KIV
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        }

                                                    </div>
                                                </td>


                                            </tr>
                                            index++;
                                        }
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Final Submit Button -->
        @*         <div class="row mt-4">
                    <div class="col-md-12 text-end">
                        <button type="submit" class="btn btn-primary">Update Inspection</button>
                    </div>
                </div> *@
            </form>
        </div>
    </div>
</div>

<div class="modal fade" id="commentModal" tabindex="-1" aria-labelledby="commentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="commentModalLabel">Add Comment</h5>
                <button type="button" class="close" data-dismiss="modal"><span>&times;</span></button>
            </div>
            <div class="modal-body">
                <textarea id="commentText" class="form-control" rows="4" placeholder="Enter your comment here..." required></textarea>
                <input type="hidden" id="hiddenInspectionId" />
                <input type="hidden" id="hiddenStatus" />
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusWithComment()">Submit</button>
            </div>
        </div>
    </div>
</div>


<!-- Complete Inspection Modal -->
<div class="modal fade" id="completeInspectionModal" tabindex="-1" role="dialog" aria-labelledby="completeInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="completeInspectionForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="completeInspectionModalLabel">Complete Inspection</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="completeInspectionId" name="inspectionId" />
                    <div class="form-group">
                        <label>After Image</label>
                        <div class="input-group  mt-3">
                            <input type="file" class="file-upload-default d-none" id="afterImageInput">
                            <input type="text" class="form-control file-upload-info" disabled placeholder="Upload image">
                            <div class="input-group-append">
                                <button class="file-upload-browse btn btn-primary py-0 px-2" type="button">Upload</button>
                            </div>

                        </div>
                    </div>
                    <div class="form-group">
                        <label for="completionDateInput">Completion Date</label>
                        <input type="date" class="form-control" id="completionDateInput" name="completionDate" required />
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Complete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Rectification Remarks Modal -->
<div class="modal fade" id="rectificationModal" tabindex="-1" role="dialog" aria-labelledby="rectificationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="rectificationForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="rectificationModalLabel">Cannot Rectify - Remarks</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="rectificationInspectionId" name="inspectionId" />
                    <div class="form-group">
                        <label for="rectificationRemarks">Remarks</label>
                        <textarea class="form-control" id="rectificationRemarks" name="remarks" rows="4" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Observation Modal -->
<div class="modal fade" id="observationModal" tabindex="-1" aria-labelledby="observationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="observationForm">
                <div class="modal-header">
                    <h5 class="modal-title">New Observation</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <input type="hidden" id="ObservationAttachmentBase64" />
                                <input type="hidden" id="RecommendationAttachmentBase64" />
                                <label>Location <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="ObservationLocationId" name="LocationId" placeholder="Location" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Action Party <span class="text-danger">*</span></label>
                                <select id="ActionPartyId" name="ActionPartyId" class="form-control js-example-basic-multiple w-100" multiple="multiple">
                                    <option value="">Select Action Party</option>
                                </select>
                            </div>
                        </div>
                    @*     <div class="col-md-6">
                            <div class="form-group">
                                <label>Type <span class="text-danger">*</span></label>
                                <select id="TypeStatus" class="form-control">
                                    <option value="1">Positive</option>
                                    <option value="2">For Action</option>
                                </select>
                            </div>
                        </div> *@
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group d-flex align-items-center gap-2">
                                <span id="switchLabel" class="fw-semibold mr-2">Positive</span>
                                <label class="toggle-switch mb-0">
                                    <input type="checkbox" id="TypeStatus" checked>
                                    <span class="toggle-slider round"></span>
                                </label>
                            </div>
                        </div>

                     @*    <div class="col-md-6">
                            <div class="form-group">
                                <label>Contact Person <span class="text-danger">*</span></label>
                                <select class="form-control" id="ContactPersonId" name="ContactPersonId">
                                    <option value="">Select Contact Person</option>
                                </select>
                            </div>
                        </div> *@
                    </div>
             
                    <!-- Observation Upload -->
                    <div class="form-group">
                        <label>Observation</label>
                        <textarea class="form-control" id="tinyMceExample" name="Observation"></textarea>

                        <div class="custom-file-upload mt-2">
                            <input type="file" class="d-none" id="ObservationAttachment" accept="image/*">
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info col-md-3" id="ObservationFileName" readonly placeholder="Upload Image">
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="observationUploadBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeObservationFile">Remove</button>
                                </div>
                            </div>
                            <div class="mt-2 d-none" id="ObservationPreviewContainer">
                                <img id="ObservationPreview" src="" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>

                    <!-- Recommendation Upload -->
                    <div class="form-group mt-3" id="recommendationSection">
                        <label>Recommendation (If any)</label>
                        <textarea class="form-control" id="editTinyMceExample" name="Recommendation"></textarea>

                        <div class="custom-file-upload mt-2">
                            <input type="file" class="d-none" id="RecommendationAttachment" accept="image/*">
                            <div class="input-group">
                                <input type="text" class="form-control file-upload-info col-md-3" id="RecommendationFileName" readonly placeholder="Upload Image">
                                <div class="input-group-append">
                                    <button class="btn btn-primary btn-sm" type="button" id="recommendationUploadBtn">Upload</button>
                                    <button class="btn btn-danger btn-sm ml-1 d-none" type="button" id="removeRecommendationFile">Remove</button>
                                </div>
                            </div>
                            <div class="mt-2 d-none" id="RecommendationPreviewContainer">
                                <img id="RecommendationPreview" src="" class="img-thumbnail" style="max-width: 200px;" />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Add to List</button>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
        const facilityId = '@(@Model.inspectionDetails.FacilityId)';
            var currentUserId = '@Context.Session.GetString("UserId")';
     var insertInspectionObservationItems =  '@Url.Action("InsertInspectionObservationItems", "Inspection")'; 
            var completeInspection = '@Url.Action("CompleteInspection", "Inspection")';
            var getUserInspections = '@Url.Action("GetUserInspections", "Inspection")';
            var updateVerificationStatus = '@Url.Action("UpdateVerificationStatus", "Inspection")';
      var getUsersByFacilityId = '@Url.Action("GetUsersByFacilityId", "User")?facilityId=';
     var getAllFacility = '@Url.Action("GetAllFacility", "Facility")';
    var getAnnouncements = '@Url.Action("GetAllAnnouncements", "Announcement")';
                    var updateVerificationStatus = '@Url.Action("UpdateVerificationStatus", "Inspection")';
    var deleteObservationItem =  '@Url.Action("DeleteObservationItem", "Inspection")';
                          const appBaseUrl = '@Url.Content("~/")'; // ensures base path works on live
    var existingUsers = [
    @foreach (var user in Model.InspectionCategory)
    {
        @: { id: "@user.InspectionCategoryId", name: "@user.CategoryName" },
    }
            ];
</script>
@section Scripts {
    <script src="~/js/Inspection/editInspection.js"></script>
} 